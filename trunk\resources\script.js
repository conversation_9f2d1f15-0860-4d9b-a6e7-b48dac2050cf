// script.js

document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');

    // **!!!重要!!! 将此处替换为你的网站域名**
    // 确保你的域名格式正确，例如 'example.com' 或 'www.yourcompany.com'
    const YOUR_WEBSITE_DOMAIN = 'www.isumsoft.com'; // <-- 在这里替换成你的实际域名

    // 搜索功能实现
    searchButton.addEventListener('click', performGoogleSiteSearch);
    searchInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter') {
            performGoogleSiteSearch();
        }
    });

    function performGoogleSiteSearch() {
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            alert('请输入搜索内容！');
            return;
        }

        // 构建 Google 网站内搜索的 URL
        // 格式：https://www.google.com/search?q=[搜索词]+site:[你的网站域名]
        const googleSearchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchTerm)}+site:${YOUR_WEBSITE_DOMAIN}`;

        // 将用户重定向到 Google 搜索结果页
        // 如果你想在新标签页打开搜索结果，可以使用：window.open(googleSearchUrl, '_blank');
        window.location.href = googleSearchUrl;
    }
});