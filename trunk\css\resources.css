/* 基础样式和重置 */
body {
    margin: 0;
    padding: 0;
    background-color: #e3f2fd; /* 更柔和的浅蓝色背景 */
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px; /* 增加最大宽度以适应更多卡片 */
    margin: 0 auto;
    padding: 0 20px;
}

/* 主头部样式 */
.main-header {
    background-color: #ffffff;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-header .logo a {
    font-size: 26px;
    font-weight: 700; /* 更粗的字体 */
    color: #2c3e50; /* 深色字体 */
    text-decoration: none;
}

.main-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.main-nav li {
    margin-left: 30px;
}

.main-nav a {
    text-decoration: none;
    color: #555;
    font-weight: 500;
    transition: color 0.3s ease;
}

.main-nav a:hover {
    color: #007bff;
}

/* 页面头部和搜索区 (Hero Section) */
.hero-section {
    background-color: #e3f2fd; /* 浅蓝色背景 */
    padding: 80px 0;
    text-align: center;
    color: #2c3e50;
}

.hero-section .page-title {
    font-size: 52px; /* 标题更大 */
    font-weight: 700;
    margin-bottom: 15px;
    color: #2c3e50;
}

.hero-section .subtitle {
    font-size: 20px;
    color: #555;
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.search-bar-container {
    display: flex;
    justify-content: center;
    max-width: 700px; /* 搜索框区域更宽 */
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px; /* 圆角更大 */
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1); /* 更明显的阴影 */
    border: 1px solid #e0e0e0; /* 增加边框 */
}

#search-input {
    flex-grow: 1;
    border: none;
    padding: 18px 25px; /* 输入框更大 */
    font-size: 18px;
    outline: none;
}

#search-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 18px 35px; /* 按钮更大 */
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

#search-button:hover {
    background-color: #0056b3;
    transform: translateY(-2px); /* 悬停微动效果 */
}

/* 主内容区 - 卡片网格布局 */
.main-content {
    padding: 60px 0;
}

.card-grid {
    display: grid; /* 使用 CSS Grid 布局 */
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* 自动适应列数，每列最小300px */
    gap: 30px; /* 卡片之间的间距 */
}

.resource-card {
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* 卡片阴影 */
    padding: 30px;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    white-space: nowrap;        /* 强制文本不换行 */
    overflow: hidden;           /* 隐藏超出容器的内容 */
    text-overflow: ellipsis;    /* 在内容被剪切时显示省略号 */
    display: block;
}

.resource-card:hover {
    transform: translateY(-5px); /* 悬停上浮效果 */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15); /* 悬停阴影加深 */
}

.card-icon {
    font-size: 48px; /* 图标大小 */
    color: #007bff; /* 图标颜色 */
    margin-bottom: 20px;
    text-align: center; /* 图标居中 */
}

.card-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 10px;
    text-align: center; /* 标题居中 */
}

.card-subtitle {
    font-size: 16px;
    color: #777;
    margin-bottom: 25px;
    text-align: center; /* 副标题居中 */
}

.card-links {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    flex-grow: 1; /* 让链接列表占据可用空间，使“查看所有”链接底部对齐 */
    white-space: nowrap;        /* 强制文本不换行 */
    overflow: hidden;           /* 隐藏超出容器的内容 */
    text-overflow: ellipsis;    /* 在内容被剪切时显示省略号 */
    display: block;
    width: 250px; 
}

.card-links li {
    margin-bottom: 12px;
}

.card-links li:last-child {
    margin-bottom: 0;
}

.card-links a {
    text-decoration: none;
    color: #007bff;
    font-size: 16px;
    transition: color 0.3s ease;
    display: block; /* 让链接可以点击的区域更大 */
}

.card-links a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.view-all {
    display: block;
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee; /* 分隔线 */
    text-decoration: none;
    color: #007bff;
    font-weight: 500;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .card-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* 中等屏幕显示更多列或调整最小宽度 */
    }
}

@media (max-width: 768px) {
    /* 头部调整 */
    .main-header .container {
        flex-direction: column;
    }
    .main-nav ul {
        margin-top: 15px;
        flex-wrap: wrap; /* 导航项换行 */
        justify-content: center;
        gap: 15px; /* 导航项间距 */
    }
    .main-nav li {
        margin-left: 0; /* 移除原有左边距 */
    }

    /* Hero Section 调整 */
    .hero-section {
        padding: 60px 0;
    }
    .hero-section .page-title {
        font-size: 40px;
    }
    .hero-section .subtitle {
        font-size: 17px;
        padding: 0 15px;
    }
    .search-bar-container {
        flex-direction: column; /* 搜索框和按钮垂直堆叠 */
        max-width: 90%;
        box-shadow: none; /* 手机端移除阴影，更简洁 */
        border: none; /* 手机端移除边框 */
        background-color: transparent; /* 手机端背景透明 */
    }
    #search-input,
    #search-button {
        width: 100%;
        box-sizing: border-box; /* 确保 padding 不增加宽度 */
        border-radius: 8px; /* 按钮和输入框圆角 */
        margin-bottom: 15px; /* 间距 */
        border: 1px solid #ccc; /* 手机端输入框边框 */
    }
    #search-input {
        background-color: #fff; /* 手机端输入框背景 */
    }
    #search-button {
        margin-bottom: 0;
    }

    /* 卡片网格调整 */
    .card-grid {
        grid-template-columns: 1fr; /* 小屏幕显示单列 */
        gap: 25px; /* 卡片间距 */
        padding: 0 20px; /* 左右内边距 */
    }
    .resource-card {
        padding: 25px;
    }
    .card-icon {
        font-size: 40px;
    }
    .card-title {
        font-size: 24px;
    }
    .card-subtitle {
        font-size: 15px;
    }
    .card-links a {
        font-size: 15px;
    }
}